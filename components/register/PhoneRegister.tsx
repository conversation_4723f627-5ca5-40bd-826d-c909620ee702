"use client";

import { useState, useEffect, useMemo } from "react";
import { X, Check } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { CountryCodeSelector } from "./country-code-select";
import { cn } from "@/lib/utils";
import { IEmailPassword, IPhonepassword } from "@/server/login";
import { useEmailSign, usePhonePasswordLogin } from "@/hooks/queryHook";
import { useRouter } from "next/navigation";

export function PerfectPhoneRegistration() {
  const [countryCode, setCountryCode] = useState("86");
  const [mobileNumber, setPhoneNumber] = useState("");
  const [verificationCode, setVerificationCode] = useState("");
  const [agreedToTerms, setAgreedToTerms] = useState(false);
  const [codeSent, setCodeSent] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [signinType, setSigninType] = useState("confirmation_code");
  const [isMobileShow, setMobileShow] = useState(true);
  const [password, setPassword] = useState("");
  const [email, setEmail] = useState("");

  const router = useRouter();

  const getLoginType = useMemo(() => {
    switch (signinType) {
      case "confirmation_code":
        if (isMobileShow) {
          return "phoneCode";
        } else {
          return "emailCode";
        }
      case "password":
        if (isMobileShow) {
          return "phonePassword";
        } else {
          return "emailPassword";
        }
    }
  }, [signinType, isMobileShow]);

  const isFormValid = useMemo(() => {
    switch (getLoginType) {
      case "phoneCode":
        return (
          codeSent &&
          mobileNumber.trim() !== "" &&
          verificationCode.trim() !== ""
        );
      case "emailCode":
        return (
          codeSent && email.trim() !== "" && verificationCode.trim() !== ""
        );
      case "phonePassword":
        return mobileNumber.trim() !== "" && password.trim() !== "";
      case "emailPassword":
        return email.trim() !== "" && password.trim() !== "";
    }
  }, [getLoginType, mobileNumber, email, password, verificationCode]);

  // 倒计时逻辑
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown]);

  const phonePasswordLogin = usePhonePasswordLogin();
  const emailSign = useEmailSign();

  const handlePhonePasswordLogin = async (data: IPhonepassword) => {
    try {
      phonePasswordLogin.mutate(data, {
        onSuccess: (data) => {
          console.log("data", data);
          console.log("登录成功！");
          localStorage.setItem("token", data.token.value);
          router.push("/");
          // 处理登录成功的逻辑
        },
        onError: (error) => {
          console.log("登录失败，请检查账号密码");
        },
      });
    } catch (error) {
      console.log(error);
    }
  };

  const handleEmailSign = async (data: IEmailPassword) => {
    try {
      emailSign.mutate(data, {
        onSuccess: (data) => {
          console.log("data", data);
          console.log("登录成功！");
          localStorage.setItem("token", data.token.value);
          router.push("/");
          // 处理登录成功的逻辑
        },
        onError: (error) => {
          console.log("登录失败，请检查账号密码");
        },
      });
    } catch (error) {
      console.log(error);
    }
  };

  const handleSendCode = async () => {
    if (mobileNumber.trim() && countdown === 0) {
      setIsLoading(true);
      // 模拟发送验证码
      await new Promise((resolve) => setTimeout(resolve, 1000));
      setCodeSent(true);
      setCountdown(60);
      setIsLoading(false);
    }
  };

  const handleLogin = async () => {
    console.log(isFormValid);
    if (isFormValid) {
      setIsLoading(true);

      if (isMobileShow) {
        handlePhonePasswordLogin({
          code: verificationCode,
          countryCode,
          mobileNumber,
          signinType: signinType,
          extra: {},
          clientId: "100033",
          password,
        });
      } else {
        handleEmailSign({
          code: verificationCode,
          email,
          signinType: signinType,
          extra: {},
          clientId: "100033",
          password,
        });
      }

      setIsLoading(false);
      // 这里可以添加登录成功后的跳转逻辑
    }
  };

  mobileNumber.trim();
  // mobileNumber.trim() && verificationCode.trim() && agreedToTerms;
  const canSendCode = mobileNumber.trim() && countdown === 0;

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-3xl shadow-xl max-w-4xl w-full overflow-hidden">
        <div className="flex min-h-[600px]">
          {/* Left side - Registration Form */}
          <div className="flex-1 p-8 max-w-md relative">
            {/* Close button */}
            <div className="flex justify-end mb-6">
              <button className="text-gray-400 hover:text-gray-600 transition-colors">
                <X className="w-6 h-6" />
              </button>
            </div>

            {/* Header */}
            <h1 className="text-2xl font-semibold text-gray-900 mb-8">
              Welcome to TanTan
            </h1>

            {/* 邮箱验证码登录 */}
            {getLoginType == "emailCode" && (
              <>
                {/* Email Input */}
                <div className="mb-4">
                  <Input
                    type="email"
                    placeholder="Your email address"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="w-full focus:border-orange-500 focus:ring-orange-500"
                  />
                </div>

                {/* Verification Code */}
                <div className="mb-4">
                  <div className="flex gap-2">
                    <Input
                      disabled={!codeSent}
                      type="text"
                      placeholder="Verification Code"
                      value={verificationCode}
                      onChange={(e) => setVerificationCode(e.target.value)}
                      className="flex-1 focus:border-orange-500 focus:ring-orange-500"
                      maxLength={6}
                    />
                    <Button
                      onClick={handleSendCode}
                      variant="outline"
                      className={cn(
                        "px-6 min-w-[100px] text-orange-500 border-orange-500 hover:bg-orange-50",
                        (!canSendCode || isLoading) &&
                          "opacity-50 cursor-not-allowed"
                      )}
                      disabled={!canSendCode || isLoading}
                    >
                      {isLoading ? (
                        <div className="w-4 h-4 border-2 border-orange-500 border-t-transparent rounded-full animate-spin" />
                      ) : countdown > 0 ? (
                        `${countdown}s`
                      ) : (
                        "Send Code"
                      )}
                    </Button>
                  </div>
                  {/* {codeSent && countdown === 0 && (
                    <p className="text-sm text-green-600 mt-1 flex items-center gap-1">
                      <Check className="w-4 h-4" />
                      Verification code sent successfully
                    </p>
                  )} */}
                </div>
              </>
            )}

            {/* 邮箱密码登录 */}
            {getLoginType == "emailPassword" && (
              <>
                {/* Email Input */}
                <div className="mb-4">
                  <Input
                    type="email"
                    placeholder="Your email address"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="w-full focus:border-orange-500 focus:ring-orange-500"
                  />
                </div>

                {/* Verification Code */}
                <div className="mb-4">
                  <Input
                    type="password"
                    placeholder="Password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="flex-1 focus:border-orange-500 focus:ring-orange-500"
                  />
                </div>
              </>
            )}

            {/* 手机验证码 */}
            {getLoginType === "phoneCode" && (
              <>
                <div className="mb-4 relative">
                  <div className="flex">
                    <CountryCodeSelector
                      value={countryCode}
                      onChange={setCountryCode}
                    />
                    <Input
                      type="tel"
                      placeholder="Your Phone number"
                      value={mobileNumber}
                      onChange={(e) => setPhoneNumber(e.target.value)}
                      className="flex-1 rounded-l-none border-l-0 focus:border-orange-500 focus:ring-orange-500 text-gray-500"
                    />
                  </div>
                </div>

                <div className="mb-4">
                  <div className="flex gap-2">
                    <Input
                      type="text"
                      disabled={!codeSent}
                      placeholder="Verification Code"
                      value={verificationCode}
                      onChange={(e) => setVerificationCode(e.target.value)}
                      className={
                        (cn(
                          "flex-1 focus:border-orange-500 focus:ring-orange-500"
                        ),
                        codeSent ? "focus:bg-white" : "bg-gray-300 ")
                      }
                      maxLength={6}
                    />
                    <Button
                      onClick={handleSendCode}
                      variant="outline"
                      className={cn(
                        "px-6 min-w-[100px] text-orange-500 border-orange-500 hover:bg-orange-50",
                        (!canSendCode || isLoading) &&
                          "opacity-50 cursor-not-allowed"
                      )}
                      disabled={!canSendCode || isLoading}
                    >
                      {isLoading ? (
                        <div className="w-4 h-4 border-2 border-orange-500 border-t-transparent rounded-full animate-spin" />
                      ) : countdown > 0 ? (
                        `${countdown}s`
                      ) : (
                        "Send Code"
                      )}
                    </Button>
                  </div>
                </div>
              </>
            )}

            {/* 手机密码登录 */}

            {getLoginType === "phonePassword" && (
              <div className="mb-4 relative">
                <div className="flex">
                  <CountryCodeSelector
                    value={countryCode}
                    onChange={setCountryCode}
                  />
                  <Input
                    type="tel"
                    placeholder="Your Phone number"
                    value={mobileNumber}
                    onChange={(e) => setPhoneNumber(e.target.value)}
                    className="flex-1 rounded-l-none border-l-0 focus:border-orange-500 focus:ring-orange-500 text-gray-500"
                  />
                </div>
              </div>
            )}

            {getLoginType === "phonePassword" && (
              <div className="mb-4">
                <Input
                  type="password"
                  placeholder="Password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="flex-1 focus:border-orange-500 focus:ring-orange-500"
                />
              </div>
            )}

            {/* Password Link */}
            <div className="mb-6">
              <div className="text-sm text-gray-500">
                Verification code issue?{" "}
                {signinType === "confirmation_code" ? (
                  <button
                    onClick={() => {
                      setSigninType("password");
                      setVerificationCode("");
                      setPassword("");
                    }}
                    className="text-orange-500 hover:text-orange-600 underline transition-colors"
                  >
                    Try password
                  </button>
                ) : (
                  <button
                    onClick={() => {
                      setSigninType("confirmation_code");
                      setVerificationCode("");
                      setPassword("");
                    }}
                    className="text-orange-500 hover:text-orange-600 underline transition-colors"
                  >
                    Try verification code
                  </button>
                )}
              </div>
            </div>

            {/* Login Button */}
            <Button
              onClick={handleLogin}
              className={cn(
                "w-full py-3 mb-6 transition-all duration-200",
                isFormValid
                  ? "bg-orange-500 text-white hover:bg-orange-600"
                  : "bg-gray-300 text-gray-500 cursor-not-allowed"
              )}
              disabled={!isFormValid || isLoading}
            >
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  Logging in...
                </div>
              ) : (
                "Log In"
              )}
            </Button>

            {/* OR Divider */}
            <div className="flex items-center mb-6">
              <div className="flex-1 border-t border-gray-200"></div>
              <span className="px-4 text-sm text-gray-500">OR</span>
              <div className="flex-1 border-t border-gray-200"></div>
            </div>

            {/* Email Login Link */}
            <div className="text-center mb-6">
              {!isMobileShow ? (
                <button
                  onClick={() => setMobileShow(true)}
                  className="text-gray-700 hover:text-gray-900 font-medium transition-colors"
                >
                  Log in with phone
                </button>
              ) : (
                <button
                  onClick={() => setMobileShow(false)}
                  className="text-gray-700 hover:text-gray-900 font-medium transition-colors"
                >
                  Log in with email
                </button>
              )}
            </div>

            {/* Terms Agreement */}
            <div className="flex items-start gap-3">
              {/* <Checkbox
                id="terms"
                checked={agreedToTerms}
                onCheckedChange={(checked) =>
                  setAgreedToTerms(checked as boolean)
                }
                className="mt-1"
              /> */}
              <label
                htmlFor="terms"
                className="text-sm text-gray-600 leading-relaxed cursor-pointer"
              >
                I have read and agree to{" "}
                <button className="text-orange-500 hover:text-orange-600 underline transition-colors">
                  TanTan User Agreement
                </button>{" "}
                &{" "}
                <button className="text-orange-500 hover:text-orange-600 underline transition-colors">
                  Privacy Policy
                </button>
              </label>
            </div>
          </div>

          {/* Right side - App Download */}
          <div className="flex-1 bg-white p-8 flex flex-col items-center justify-center">
            {/* Mascot Character */}
            <div className="mb-6">
              <div className="w-20 h-20 bg-gradient-to-b from-orange-400 to-orange-500 rounded-2xl flex items-center justify-center shadow-md relative overflow-hidden">
                <div className="absolute top-[40%] w-full">
                  <div className="flex justify-center gap-6">
                    <div className="w-2 h-2 rounded-full bg-black"></div>
                    <div className="w-2 h-2 rounded-full bg-black"></div>
                  </div>
                  <div className="mt-2 w-3 h-3 bg-black mx-auto rounded-full"></div>
                </div>
              </div>
            </div>

            {/* Download Section */}
            <div className="text-center max-w-xs">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                Download the APP
              </h2>
              <p className="text-gray-600 mb-8 leading-relaxed text-sm">
                No TanTan account? Download the app today to create your account
                and unlock full access to all features!
              </p>

              <p className="text-sm text-gray-700 mb-4">
                You can get the app from here:
              </p>

              {/* App Store Buttons */}
              <div className="flex justify-center gap-3">
                <button className="flex items-center justify-center gap-2 bg-black text-white px-3 py-2 rounded-lg hover:bg-gray-800 transition-all duration-200">
                  <div className="w-5 h-5">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                      <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z" />
                    </svg>
                  </div>
                  <div className="text-left">
                    <div className="text-[10px] leading-tight">
                      Download on the
                    </div>
                    <div className="text-xs font-semibold leading-tight">
                      App Store
                    </div>
                  </div>
                </button>

                <button className="flex items-center justify-center gap-2 bg-black text-white px-3 py-2 rounded-lg hover:bg-gray-800 transition-all duration-200">
                  <div className="w-5 h-5">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                      <path d="M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.6 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.9 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z" />
                    </svg>
                  </div>
                  <div className="text-left">
                    <div className="text-[10px] leading-tight">GET IT ON</div>
                    <div className="text-xs font-semibold leading-tight">
                      Google Play
                    </div>
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
