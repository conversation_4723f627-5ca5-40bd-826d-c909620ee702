"use client";

import { use, useEffect, useRef, useState } from "react";
import { ChevronLeft, Check, X, LogOut, HelpCircle } from "lucide-react";
import { UserAvatar } from "./user-avatar";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import { tabs, themeStyles, mobileThemeStyles, plans } from "@/lib/themes";
import Image from "next/image";
import { usePhonePasswordLogin } from "@/hooks/queryHook";
import { useCheckoutMerchandises, useCheckoutOrder } from "@/hooks/queryHook";
import { IPhonepassword } from "@/server/login";
import { textUrlMap, TRANSFORM_PRODUCT_NAME } from "@/lib/enums";
import styles from "./subscription-page.module.css";
import { Plan, ShopItem } from "@/types/payment";
import { useRouter } from "next/navigation";
import { getToken } from "@/lib/hmacv2";

export function SubscriptionPage() {
  const router = useRouter();

  const [selectedPlan, setSelectedPlan] = useState<Plan>({} as Plan);
  const [shopList, setShopList] = useState<ShopItem[]>([]);
  const [planList, setPlanList] = useState<Plan[]>([]);
  const [activeTab, setActiveTab] = useState("vip");
  const [cardNumber, setCardNumber] = useState("1234 1234 1234 1234");
  const [cardholderName, setCardholderName] = useState("Jordan Smith");
  const [expiryDate, setExpiryDate] = useState("MM/YY");
  const [securityCode, setSecurityCode] = useState("CVV");
  const [showErrorCardNumber, setShowErrorCardNumber] = useState(false);
  const [showErrorExpiryDate, setShowErrorExpiryDate] = useState(false);
  const [showErrorCvv, setShowErrorCvv] = useState(false);
  const [showErrorCardNotSupport, setShowErrorCardNotSupport] = useState(false);
  const selectedPlanRef = useRef(selectedPlan);

  const currentTheme = themeStyles[activeTab as keyof typeof themeStyles];
  const currentMobileTheme =
    mobileThemeStyles[activeTab as keyof typeof mobileThemeStyles];

  const handleTabChange = (key: ShopItem["type"]) => {
    setActiveTab(key);
    const planList = shopList.find((shop) => shop.type === key)?.plans || [];
    setPlanList(planList);
    setSelectedPlan(planList[1]);
  };

  const phonePasswordLogin = usePhonePasswordLogin();
  const checkoutOrder = useCheckoutOrder();
  const checkoutMerchandises = useCheckoutMerchandises();

  //分类并且更改渠道结构
  const getMerchandiseClassByCategory = (merchandises: any): ShopItem[] => {
    return Object.values(
      merchandises.reduce((acc: any, cur: Plans) => {
        // 更改数据结构
        cur.id = cur?.defaultStockKeepUnit?.id?.toString() ?? "";
        // category ===》 平台 + 支付方式 + 商品名，例：codapay_linepay_vip
        const category = TRANSFORM_PRODUCT_NAME[cur?.productName ?? ""];

        cur.name = category;
        cur.categoryName = category;

        const percent =
          (parseInt(cur.defaultStockKeepUnit.prices.price) /
            (parseInt(cur.defaultStockKeepUnit.prices.unitPrice) *
              cur.quantity)) *
          100;

        cur.discount = percent !== 100 ? percent.toFixed(0) + "%" : "";

        // 过滤掉非规范商品名称/类目名称
        if (!Object.values(TRANSFORM_PRODUCT_NAME).includes(category)) {
          console.warn("lsf1", acc);
          return acc;
        }

        if (!acc[category]) {
          acc[category] = {
            type: category,
            fold: false,
            plans: [cur],
          };
          return acc;
        }
        acc[category].plans.push(cur);
        console.log(acc);
        return acc;
      }, {})
    );
  };

  const handlePhonePasswordLogin = async (data: IPhonepassword) => {
    try {
      phonePasswordLogin.mutate(data, {
        onSuccess: (data) => {
          console.log("data", data);
          console.log("登录成功！");
          // 处理登录成功的逻辑
        },
        onError: (error) => {
          console.log("登录失败，请检查账号密码");
        },
      });
    } catch (error) {
      console.log(error);
    }
  };
  const handleCheckoutMerchandises = async (): Promise<void> => {
    checkoutMerchandises.mutate(undefined, {
      onSuccess: (data) => {
        const {
          data: { merchandises },
        } = data;

        console.log(merchandises);

        console.log(
          "getMerchandiseClassByCategory:",
          getMerchandiseClassByCategory(merchandises)
        );
        const shopList = getMerchandiseClassByCategory(merchandises);

        shopList.forEach((shopItem) => {
          shopItem.plans.sort((a: any, b: any) => a.quantity - b.quantity);
        });

        setShopList(shopList);
      },
      onError: (error) => {
        console.log(error);
      },
    });
  };
  const handleCheckoutOrder = async (data: any) => {
    checkoutOrder.mutate(data, {
      onSuccess: (data) => {
        console.log(data);
        console.log("创建订单成功！");
      },
      onError: (error) => {
        console.log("订单失败");
      },
    });
  };

  // 错误信息管理
  const errorMessage = {
    show: (element: string) => {
      switch (element) {
        case "card-number":
          setShowErrorCardNumber(true);
          break;
        case "expiry-date":
          setShowErrorExpiryDate(true);
          break;
        case "cvv":
          setShowErrorCvv(true);
          break;
        case "card-not-support":
          setShowErrorCardNotSupport(true);
      }
    },
    hide: (element: string) => {
      switch (element) {
        case "card-number":
          setShowErrorCardNumber(false);
          break;
        case "expiry-date":
          setShowErrorExpiryDate(false);
          break;
        case "cvv":
          setShowErrorCvv(false);
          break;
        case "card-not-support":
          setShowErrorCardNotSupport(false);
      }
    },
  };
  //初始化checkoutjs
  const initFrames = () => {
    console.log(process.env.NEXT_PUBLIC_CHECKOUT_PUBLIC_KEY);
    window.Frames?.init({
      publicKey: process.env.NEXT_PUBLIC_CHECKOUT_PUBLIC_KEY,
      style: {
        base: {
          color: "#red",
          fontSize: "15px",
          paddingLeft: "12px",
          fontWeight: "600",
        },
        placeholder: {
          base: {
            color: "rgba(0, 0, 0, .3)", //'#d0d0d0'
          },
        },
        focus: {
          border: "2px solid #000000",
          borderRadius: "10px",
        },
      },
    });

    // 表单项目更改,form校验
    window.Frames?.addEventHandler(
      window.Frames.Events.FRAME_VALIDATION_CHANGED,
      (event: any) => {
        // if (!getToken()) {
        //   console.log("token is null");
        //   router.push("/login");
        // }

        // const element = event.element;
        // if (event.isValid || event.isEmpty) {
        //   this.errorMessage.hide(element);
        // } else {
        //   this.errorMessage.show(element);
        // }
      }
    );
    // 银行卡验证
    window.Frames?.addEventHandler(
      window.Frames.Events.CARD_VALIDATION_CHANGED,
      (valid: any) => {
        // if (!valid.isValid || !this.name.trim()) {
        //   this.disabledSubmit = true;
        // } else {
        //   this.disabledSubmit = false;
        // }
      }
    );
    //输入卡号时检测到支付方式更改事回调
    const supportCards = ["mastercard", "visa"];
    window.Frames?.addEventHandler(
      window.Frames.Events.PAYMENT_METHOD_CHANGED,
      (event: any) => {
        if (event && event.paymentMethod) {
          if (supportCards.indexOf(event.paymentMethod.toLowerCase()) >= 0) {
            //
          } else {
            //不支持卡片回调
          }
        } else {
          //
        }
      }
    );
    // 银行的token失效
    window.Frames?.addEventHandler(
      window.Frames.Events.CARD_TOKENIZATION_FAILED,
      () => {
        // this.isSubmitHistoryBtnLoading = false;
        // logBuyFailurePopup();
        // this.isShowFailed = true;
        // window.Frames.enableSubmitForm();
      }
    );
    // cardToken
    window.Frames?.addEventHandler(
      window.Frames.Events.CARD_TOKENIZED,
      (event: any) => {
        console.log("支付");

        const selectedPlan = selectedPlanRef.current;

        console.log("selectedPlan:", selectedPlan);
        console.log("selectedPlan:", selectedPlanRef.current);

        handleCheckoutOrder({
          paymentMethod: "card",
          token: event.token,
          cardType: "token",
          currency: selectedPlan?.defaultStockKeepUnit?.prices?.currencyCode,
          ...{
            symbol: selectedPlan?.defaultStockKeepUnit?.prices?.currencySymbol,
            type: "sub", // 订阅类固定sub,
            name: `${selectedPlan.quantity}%@ month(s)${selectedPlan.productName}`,
            itemId: selectedPlan.id,
            value: selectedPlan.defaultStockKeepUnit.prices.price,
          },
        });

        // beforeCheckRepeat(
        //   Object.assign(
        //     {
        //       cardType: "token",
        //       currency: this.currency, //this.user.defaultCurrencyCode, //this.currency,
        //       name: this.name.trim(),
        //     },
        //     event
        //   ),
        //   this.account
        // );
      }
    );
  };

  const handlePayment = () => {
    window.Frames.enableSubmitForm();
    window.Frames.cardholder = {
      name: cardholderName,
    };
    window.Frames.submitCard();
  };

  useEffect(() => {
    try {
      initFrames();
      handlePhonePasswordLogin({
        code: 7169,
        password: "123456",
        countryCode: 93,
        mobileNumber: "9870987",
        signinType: "password",
        extra: {},
        clientId: "100033",
      });
      handleCheckoutMerchandises();
    } catch (error) {
      console.error("Error:", error);
    }
  }, []);

  useEffect(() => {
    shopList.length && handleTabChange("vip");
  }, [shopList]);

  useEffect(() => {
    console.log(`selectedPlan:`, selectedPlan);

    selectedPlanRef.current = selectedPlan;
  }, [selectedPlan.id]);

  return (
    <div className="min-h-screen">
      {/* Mobile Layout - 保持不变 */}
      <div
        className={cn("md:hidden min-h-screen ", currentMobileTheme.background)}
      >
        {/* Mobile Header */}
        <div className="bg-gray-800 px-4 py-4 flex items-center justify-between">
          <ChevronLeft className="w-6 h-6 text-white" strokeWidth={2} />
          <div className="w-12 h-12 bg-gradient-to-b from-orange-400 to-orange-500 rounded-2xl flex items-center justify-center">
            <div className="text-white text-xl ">🦊</div>
          </div>
          <div className="w-6"></div>
        </div>

        {/* User Profile Section */}
        <div className="px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <UserAvatar
                src="/placeholder.svg?height=48&width=48"
                alt="Anita"
                size={48}
              />
              <div>
                <div className="flex items-center gap-2 mb-1">
                  <span
                    className={cn("font-semibold text-lg", currentTheme.text)}
                  >
                    Anita
                  </span>
                  <span className="bg-orange-400 text-white text-xs px-2 py-1 rounded-md ">
                    {activeTab.toUpperCase()}
                  </span>
                </div>
                <div className={cn("text-sm", currentTheme.textSecondary)}>
                  VIP benefits expire 2025/09/10
                </div>
              </div>
            </div>
            <button
              className={cn(
                "text-sm font-medium px-3 py-2 rounded-lg bg-white/20",
                currentTheme.textSecondary
              )}
            >
              Log out
            </button>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="px-4 pb-8">
          <div className="flex gap-3 bg-[rgba(255,255,255,0.4)]">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => handleTabChange(tab.type)}
                className={cn(
                  "px-4 py-3 rounded-2xl text-sm font-semibold transition-colors flex-1 text-center",
                  tab.label === activeTab
                    ? currentTheme.tabSelected
                    : currentTheme.tabUnselected
                )}
              ></button>
            ))}
          </div>
        </div>

        {/* Icon */}
        <div className="flex justify-center pb-12">
          <div
            className={cn(
              "w-[160px] h-[152px] rounded-3xl flex items-center justify-center ",
              currentMobileTheme.bg
            )}
          >
            <div className="text-4xl">{}</div>
          </div>
        </div>

        {/* Subscription Plans */}
        {/* <div className="px-4 pb-8">
          <div className="flex gap-4 h-[134px] ">
            {plans.map((plan) => (
              <div
                key={plan.id}
                onClick={() => setSelectedPlan(plan.id)}
                className={cn(
                  " p-0.5 flex-1 rounded-2xl cursor-pointer transition-all  relative shadow-md border-2",
                  selectedPlan === plan.id
                    ? cn(
                        currentTheme.selectedCard,
                        currentTheme.selectedCardBorder
                      )
                    : currentTheme.unselectedCard
                )}
              >
                {plan && selectedPlan === plan.id && (
                  <div
                    className={cn(
                      "w-[81px] h-[24px] absolute -top-3 left-3 text-gray-800 text-xs  px-3 py-1 rounded-full shadow-sm flex justify-center ",
                      currentMobileTheme.selectedCard,
                      selectedPlan === plan.id
                        ? currentMobileTheme.planSelectText
                        : currentMobileTheme.planUnSelectText,
                      selectedPlan === plan.id
                        ? currentMobileTheme.selectedCardBorder
                        : currentMobileTheme.unSelectedCardBorder,
                      selectedPlan === plan.id
                        ? currentMobileTheme.selectedCard
                        : currentMobileTheme.unselectedCard
                    )}
                  >
                    {16}
                  </div>
                )}

                {selectedPlan === plan.id ? (
                  <>
                    <div className=" h-[97px] bg-white  rounded-t-xl pt-4 text-center">
                      <div className="text-[32px] leading-[32px]  text-gray-800">
                        {plan.quantity}
                      </div>
                      <div className="text-[13px] leading-[13px] text-gray-500 mb-[7px]">
                        months
                      </div>
                      <div
                        className={cn(
                          "text-[13px] leding-[14px]",
                          currentMobileTheme.planPriceText
                        )}
                      >
                        {plan.defaultStockKeepUnit.prices.unitPrice}
                      </div>
                    </div>
                    <div
                      className={cn(
                        "mx-2  rounded-b-xl  text-center",
                        currentMobileTheme.selectedCard
                      )}
                    >
                      <div
                        className={cn(
                          "text-[16px] mt-1.5 font-medium",
                          currentMobileTheme.text
                        )}
                      >
                        {plan.defaultStockKeepUnit.prices.price}
                      </div>
                    </div>
                  </>
                ) : (
                  <div className="pt-[19px] text-center">
                    <div className="text-[30px] leading-[30px]  text-gray-400">
                      {plan.quantity}
                    </div>
                    <div className="text-[13px] leading-[14px] text-gray-400 mb-2">
                      months
                    </div>
                    <div className="text-[13px] leading-[14px] text-gray-400 font-medium mb-3">
                      {plan.defaultStockKeepUnit.prices.unitPrice}
                    </div>
                    <div className="text-[16px] mt-1.5 font-medium text-gray-400">
                      {plan.defaultStockKeepUnit.prices.price}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div> */}

        {/* Features */}
        <div className="px-4 pb-8">
          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
            <h3 className="text-center text-gray-400 text-sm mb-6 font-medium">
              {activeTab.toUpperCase()} Exclusive Privileges
            </h3>
            <div className="space-y-4">
              {currentTheme.features.map((feature, index) => (
                <div key={index} className="flex items-center gap-4">
                  <div className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center flex-shrink-0">
                    <Check className="w-5 h-5 text-white" strokeWidth={3} />
                  </div>
                  <div>
                    <div className="font-semibold text-gray-800 text-lg mb-1">
                      {feature.name}
                    </div>
                    <div className="text-sm text-gray-500">{feature.desc}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Pay Button */}
        <div className="px-4 pb-8">
          <Button
            className={cn(
              "w-full py-4  text-lg rounded-2xl shadow-md border-0",
              currentTheme.button
            )}
          >
            Pay
          </Button>
        </div>

        {/* Home Indicator */}
        <div className="flex justify-center pb-6">
          <div className="w-32 h-1 bg-black rounded-full"></div>
        </div>
      </div>

      {/* Desktop Layout  */}
      <div className={cn("hidden md:block min-h-screen bg-black")}>
        {/* 背景图 */}
        <div
          className={cn(
            "opacity-60 hidden md:block min-h-screen bg-[url('https://oversea.hellogroupcdn.com/s1/u/fbbfedcfd/desktop-bg.png')] bg-no-repeat bg-[length:100%_100%] "
          )}
        ></div>
        {/* 订阅页面 */}
        <div
          className={cn(
            "overflow-hidden h-[692px] w-[1024px] mx-auto pt-[24px] pl-[20px] pr-[20px] mb-[16px] absolute -translate-x-1/2 -translate-y-1/2 left-1/2 top-1/2 rounded-[36px]",
            currentTheme.background
          )}
        >
          {/* ultra背景渐变 */}
          {activeTab === "ultra" && (
            <div className="bg-[url('https://oversea.hellogroupcdn.com/s1/u/fbbfedcfd/bg_ultra_linear.png')] absolute -top-[62px] -right-[19px] -z-10 w-[577px] h-[727px] bg-[length:100%_100%] bg-no-repeat"></div>
          )}
          {/* Header */}
          <div
            className={cn(
              "rounded-3xl h-[50px] mb-[25px] flex items-center justify-between",
              currentTheme.headerBg
            )}
          >
            <div className="flex items-center gap-4">
              <UserAvatar
                src="/placeholder.svg?height=60&width=60"
                alt="Anita"
                size={60}
              />
              <div>
                <div className="flex items-center gap-3 mb-2">
                  <span
                    className={cn("text-[21px] font-medium", currentTheme.text)}
                  >
                    Anita
                  </span>
                  <span className="bg-yellow-400 text-white 800 text-sm px-3 py-1 rounded-full ">
                    VIP
                  </span>
                </div>
                <div
                  className={cn(
                    "text-sm text-gray-700 ",
                    currentTheme.textSecondary
                  )}
                >
                  Your
                  <span className="text-red-400"> VIP </span>
                  benefits will expire on
                  <span className="text-red-400"> 2025/09/10</span>. Renew or
                  upgrade now!
                </div>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <button className="flex items-center gap-2 bg-red-500 text-white px-4 py-2 rounded-lg text-sm hover:bg-red-600 transition-colors">
                <LogOut className="w-4 h-4" />
                Log out
              </button>
              <button
                className={cn("transition-colors", currentTheme.textSecondary)}
              >
                <X className="w-6 h-6" />
              </button>
            </div>
          </div>

          <div className="flex gap-8">
            {/* Left Content */}
            <div className="flex-1 w-[652px]">
              {/* Tab Navigation */}
              <div
                className={cn(
                  "flex gap-4 mb-[30px] bg-[rgba(255,255,255,0.4)] rounded-[20px] pl-1.5 pr-1.5",
                  currentTheme.tabUnselected
                )}
              >
                {shopList.map((tab) => (
                  <button
                    key={tab.type}
                    onClick={() => handleTabChange(tab.type)}
                    className={cn(
                      "px-6 py-3 rounded-2xl font-semibold transition-colors w-[160px] h-[68px] flex items-center justify-center mt-1.5 mb-1.5 flex-1",
                      tab.type === activeTab
                        ? currentTheme.tabSelected
                        : currentTheme.tabUnselected
                    )}
                  >
                    {tab.type === activeTab ? (
                      <Image
                        alt="Ultra"
                        key={tab.type}
                        height={
                          textUrlMap[tab.type as keyof typeof textUrlMap].height
                        }
                        width={
                          textUrlMap[tab.type as keyof typeof textUrlMap].width
                        }
                        src={
                          textUrlMap[tab.type as keyof typeof textUrlMap]
                            .selected
                        }
                        className=" object-cover"
                      ></Image>
                    ) : (
                      <Image
                        alt="Ultra"
                        layout="auto"
                        key={tab.type}
                        height={
                          textUrlMap[tab.type as keyof typeof textUrlMap].height
                        }
                        width={
                          textUrlMap[tab.type as keyof typeof textUrlMap].width
                        }
                        src={
                          ["vip", "see"].includes(activeTab)
                            ? textUrlMap[tab.type as keyof typeof textUrlMap]
                                .unSelected.light
                            : textUrlMap[tab.type as keyof typeof textUrlMap]
                                .unSelected.dark
                        }
                        className=" object-cover"
                      ></Image>
                    )}
                  </button>
                ))}
              </div>

              {/* Subscription Plans */}
              <div className="grid grid-cols-3 gap-6 mb-4 h-[216px] box-content">
                {planList.map((plan) => (
                  <div
                    key={plan.id}
                    onClick={() => setSelectedPlan({ ...plan })}
                    className={cn(
                      "h-full rounded-xl cursor-pointer transition-all duration-300 relative border-primary border-[4px] ",
                      selectedPlan.id === plan.id
                        ? cn(currentTheme.selectedCardBorder)
                        : cn(
                            currentTheme.unSelectedCardBorder,
                            currentTheme.cardBackground
                          )
                    )}
                  >
                    {plan.discount && selectedPlan.id === plan.id && (
                      <div
                        className={cn(
                          "text-black w-[112.5px] h-[33.33px] absolute -translate-y-1/2 -translate-x-1/2 left-1/2  text-xs  px-3 py-1 rounded-full z-10 flex justify-center  items-center",
                          currentTheme.selectedCard,
                          selectedPlan.id === plan.id
                            ? currentTheme.planSelectText
                            : currentTheme.planUnSelectText,
                          selectedPlan.id === plan.id
                            ? currentTheme.selectedCardBorder
                            : currentTheme.unSelectedCardBorder,
                          selectedPlan.id === plan.id
                            ? currentTheme.selectedCard
                            : currentTheme.unselectedCard
                        )}
                      >
                        {plan.discount}
                      </div>
                    )}

                    {selectedPlan.id === plan.id ? (
                      <>
                        {/* Selected State */}
                        <div
                          className={cn(
                            "p-6 text-center rounded-xl",
                            currentTheme.cardBackground
                          )}
                        >
                          <div
                            className={cn(
                              "text-5xl  text-gray-800 mb-2",
                              currentTheme.text
                            )}
                          >
                            {plan.quantity}
                          </div>
                          <div
                            className={cn(
                              "text-sm text-gray-500 mb-3",
                              currentTheme.textSecondary
                            )}
                          >
                            months
                          </div>
                          <div
                            className={cn(
                              "text-s  font-medium",
                              currentTheme.planPriceText
                            )}
                          >
                            ${plan.defaultStockKeepUnit.prices.unitPrice}/mo
                          </div>
                        </div>
                        <div
                          className={cn(
                            "p-6 text-center h-[70px]",
                            currentTheme.selectedCard
                          )}
                        >
                          <div
                            className={cn(
                              "text-2xl",
                              currentTheme.priceTextSelected
                            )}
                          >
                            ${plan.defaultStockKeepUnit.prices.price}
                          </div>
                        </div>
                      </>
                    ) : (
                      <div className="p-6 text-center">
                        <div className={cn("text-5xl mb-2", currentTheme.text)}>
                          {plan.quantity}
                        </div>
                        <div
                          className={cn(
                            "text-sm text-gray-800 mb-3",
                            currentTheme.text
                          )}
                        >
                          months
                        </div>
                        <div
                          className={cn(
                            "text-sm font-medium mb-4",
                            currentTheme.planPriceText
                          )}
                        >
                          ${plan.defaultStockKeepUnit.prices.unitPrice}/mo
                        </div>
                        <div
                          className={cn(
                            "text-2xl text-gray-800",
                            currentTheme.priceTextUnSelected
                          )}
                        >
                          ${plan.defaultStockKeepUnit.prices.price}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>

              {/* Terms */}
              <div
                className={cn(
                  "text-xs mb-2 mt-4 inline-block ",
                  currentTheme.textSecondary
                )}
              >
                Auto-renewable subscription, cancel anytime in App Store
                settings, automatic renewal 24 hours before the subscription
                period ends. By clicking "Continue", you agree to and accept our{" "}
                <span className="text-blue-500 underline cursor-pointer">
                  Terms of Service
                </span>{" "}
                and{" "}
                <span className="text-blue-500 underline cursor-pointer">
                  Privacy Policy
                </span>
                .
              </div>

              {/* Special Feature for SEE */}
              {currentTheme.specialFeature && (
                <div className="mb-8 flex justify-center">
                  <div className="text-center">
                    <div className="flex justify-center mb-4">
                      <div className="relative">
                        {currentTheme.specialFeature.avatars.map(
                          (avatar, index) => (
                            <div
                              key={index}
                              className={cn(
                                "absolute w-16 h-16 rounded-full border-4 border-white overflow-hidden",
                                index === 0 && "left-0 z-30",
                                index === 1 && "left-8 z-20",
                                index === 2 && "left-16 z-10"
                              )}
                            >
                              <img
                                src={avatar || "/placeholder.svg"}
                                alt="User"
                                className="w-full h-full object-cover"
                              />
                            </div>
                          )
                        )}
                        <div className="w-24 h-16"></div>
                        <div className="absolute bottom-0 right-0 w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
                          <span className="text-white text-lg">🦊</span>
                        </div>
                      </div>
                    </div>
                    <h3 className={cn("text-xl  mb-2", currentTheme.text)}>
                      {currentTheme.specialFeature.title}
                    </h3>
                    <p className={cn("text-sm", currentTheme.textSecondary)}>
                      {currentTheme.specialFeature.desc}
                    </p>
                  </div>
                </div>
              )}

              {/* Features Table */}
              <div
                className={cn(
                  "rounded-2xl overflow-scroll max-h-[199px] w-full"
                )}
              >
                {/* <div className="grid grid-cols-4 gap-0">
                
                  <div className="p-4">
                    <span className={cn("font-semibold", currentTheme.text)}>
                      Features
                    </span>
                  </div>
                  <div
                    className={cn(
                      "p-4 text-center font-semibold",
                      currentTheme.featuresHeaderBg,
                      "text-white"
                    )}
                  >
                    VIP
                  </div>
                  <div
                    className={cn(
                      "p-4 text-center font-semibold",
                      currentTheme.text
                    )}
                  >
                    Premium
                  </div>
                  <div
                    className={cn(
                      "p-4 text-center font-semibold",
                      currentTheme.text
                    )}
                  >
                    Ultra Premium
                  </div>

                  {currentTheme.features.map((feature, index) => (
                    <div
                      key={index}
                      className="col-span-4 grid grid-cols-4 gap-0 border-t border-gray-200/20"
                    >
                      <div className="p-4">
                        <div className={cn("font-medium", currentTheme.text)}>
                          {feature.name}
                        </div>
                        <div
                          className={cn("text-sm", currentTheme.textSecondary)}
                        >
                          {feature.desc}
                        </div>
                      </div>
                      <div className="p-4 text-center">
                        <Check
                          className={cn("w-5 h-5 mx-auto", "text-green-500")}
                        />
                      </div>
                      <div className="p-4 text-center">
                        <Check
                          className={cn("w-5 h-5 mx-auto", "text-green-500")}
                        />
                      </div>
                      <div className="p-4 text-center">
                        <Check
                          className={cn("w-5 h-5 mx-auto", "text-green-500")}
                        />
                      </div>
                    </div>
                  ))}
                </div> */}

                <Image
                  alt="feature"
                  height={1355}
                  width={652}
                  src={
                    "https://oversea.hellogroupcdn.com/s1/u/fbbfedcfd/vip_feature.png"
                  }
                  className="w-full"
                ></Image>
              </div>
            </div>

            {/* Right Payment Panel */}
            <div className="w-80">
              <div className={cn("rounded-2xl p-4", currentTheme.paymentBg)}>
                <div className="mb-6">
                  <div className="text-sm text-gray-500 mb-1">
                    Subscribe to Tantan
                  </div>
                  <div className="text-2xl text-gray-800">US${"618"}</div>
                </div>

                <div className="space-y-4 mb-6">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">小计</span>
                    <span className="text-gray-800">US$20.00</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500 flex items-center gap-1">
                      税 <HelpCircle className="w-3 h-3" />
                    </span>
                    <span className="text-gray-500">US$0.00</span>
                  </div>
                  <div className="border-t pt-2">
                    <div className="flex justify-between font-semibold">
                      <span className="text-gray-800">今日应付合计</span>
                      <span className="text-gray-800">US$20.00</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <RadioGroup
                    defaultValue="card"
                    className="flex items-center gap-2"
                  >
                    <RadioGroupItem value="card" id="card" />
                    <Label
                      htmlFor="card"
                      className="flex items-center gap-2 text-gray-800"
                    >
                      <div className="w-4 h-4 border border-gray-300 rounded flex items-center justify-center">
                        <div className="w-2 h-2 bg-black rounded-full"></div>
                      </div>
                      Card
                    </Label>
                  </RadioGroup>

                  <div>
                    <Label className="text-sm text-gray-700">
                      Cardholder name
                    </Label>
                    <Input
                      value={cardholderName}
                      onChange={(e) => setCardholderName(e.target.value)}
                      className="mt-1 bg-gray-50 border-gray-200"
                    />
                  </div>

                  <Label className="text-sm text-gray-700">Card number</Label>
                  <div
                    className={cn(
                      "card-number-frame text-sm text-gray-700 h-[31px] mb-[10px] bg-[#F5F5F5] rounded-lg ",
                      styles.cardNumber
                    )}
                  ></div>

                  <div className="flex">
                    <div className="flex-1 mr-[13px]">
                      <Label className="text-sm text-gray-700">
                        Expiry date
                      </Label>
                      <div className="expiry-date-frame text-sm text-gray-700 h-[31px] mb-[10px] bg-[#F5F5F5] rounded-lg "></div>
                    </div>

                    <div className="flex-1">
                      <Label className="text-sm text-gray-700">
                        Security code
                      </Label>
                      <div className="cvv-frame h-[31px] mb-[10px] bg-[#F5F5F5] rounded-lg "></div>
                    </div>
                  </div>

                  {showErrorCardNumber && (
                    <span className="error-message error-message__card-number">
                      Please enter a valid card number
                    </span>
                  )}

                  {showErrorExpiryDate && (
                    <span className="error-message error-message__expiry-date">
                      Please enter a valid expiry date
                    </span>
                  )}

                  {showErrorCvv && (
                    <span
                      className="error-message error-message__cvv"
                      v-show="error_cvv"
                    >
                      Please enter a valid cvv code
                    </span>
                  )}

                  {showErrorCardNotSupport && (
                    <span
                      className="error-message error-message__card-not-support"
                      v-show="error_card_not_support"
                    >
                      This card is temporarily not accepted, please use Visa or
                      MasterCard
                    </span>
                  )}

                  {/* <div className="card-number-frame">
                    <Label className="text-sm text-gray-700">Card number</Label>
                    <div className="relative">
                      <Input
                        value={cardNumber}
                        onChange={(e) => setCardNumber(e.target.value)}
                        className="mt-1 pr-20 bg-gray-50 border-gray-200"
                      />
                      <div className="absolute right-2 top-1/2 -translate-y-1/2 flex gap-1">
                        <div className="w-6 h-4 bg-blue-600 rounded text-white text-xs flex items-center justify-center ">
                          V
                        </div>
                        <div className="w-6 h-4 bg-red-600 rounded"></div>
                        <div className="w-6 h-4 bg-blue-500 rounded"></div>
                        <div className="w-6 h-4 bg-orange-500 rounded"></div>
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4 expiry-date-frame">
                    <div>
                      <Label className="text-sm text-gray-700">
                        Expiry date
                      </Label>
                      <Input
                        value={expiryDate}
                        onChange={(e) => setExpiryDate(e.target.value)}
                        className="mt-1 bg-gray-50 border-gray-200"
                      />
                    </div>
                    <div>
                      <Label className="text-sm text-gray-700 flex items-center gap-1">
                        Security code
                        <HelpCircle className="w-3 h-3" />
                      </Label>
                      <Input
                        value={securityCode}
                        onChange={(e) => setSecurityCode(e.target.value)}
                        className="mt-1 bg-gray-50 border-gray-200 cvv-frame"
                      />
                    </div>
                  </div> */}

                  <Button
                    onClick={() => handlePayment()}
                    className="w-full bg-black text-white hover:bg-gray-800 py-3 rounded-lg font-semibold"
                  >
                    pay
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
