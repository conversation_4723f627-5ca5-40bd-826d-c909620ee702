import { fetcher } from "@/lib/fetcher";
import { aesEncrypt } from "@/lib/utils";
import { pickBy } from "lodash-es";

export interface ResponseBase<T> {
  data: T;
  meta: {
    code: number | string;
    message: string;
  };
}

export interface IPhonepassword {
  mobileNumber: string;
  password?: string;
  clientId?: string;
  code?: number | string;
  countryCode: string | number;
  signinType: string;
  extra?: {
    [key: string]: any;
  };
}

interface IPhoneCode {
  mobileNumber: string;
  countryCode: string | number;
  clientId: string;
  captcha?: {
    captchaID: string;
    captchaOutput: string;
    genTime: string;
    lotNumber: string;
    passToken: string;
  };
  category: string;
  codeLength: number;
  language: string;
}

interface IEmailCode {
  email: string;
  countryCode: string | number;
  clientId: string;
  captcha: {
    captchaID: string;
    captchaOutput: string;
    genTime: string;
    lotNumber: string;
    passToken: string;
  };
  category: string;
  codeLength: number;
  language: string;
}

export interface IEmailPassword {
  password?: string;
  clientId?: string;
  code?: number | string;
  signinType: string;
  email: string;
  extra?: {
    [key: string]: any;
  };
}

interface LoginResponse {
  token: {
    expiresIn: number;
    userId: string;
    value: string;
  };
  extra: {
    reginTag: string;
  };
}

export const phonePasswordLogin = async (
  data: IPhonepassword
): Promise<LoginResponse> => {
  const _data = pickBy(
    {
      ...data,
      password: data?.password ? aesEncrypt(data?.password) : null,
    },
    (value) => value != null && value !== ""
  );

  const response = await fetcher<LoginResponse>("/v1/phone/signin", {
    method: "POST",
    body: JSON.stringify(_data),
    needAuth: false,
  });

  return response;
};

export const phoneCodeSend = async (
  data: IPhoneCode
): Promise<LoginResponse> => {
  const response = await fetcher<LoginResponse>("/v1/phone/code/send", {
    method: "POST",
    body: JSON.stringify(data),
    needAuth: false,
  });

  return response;
};

export const emailCodeSend = async (
  data: IEmailCode
): Promise<LoginResponse> => {
  const response = await fetcher<LoginResponse>("/v1/email/code/send", {
    method: "POST",
    body: JSON.stringify(data),
    needAuth: false,
  });

  return response;
};

export const emailSign = async (
  data: IEmailPassword
): Promise<  LoginResponse> => {
  const _data = pickBy(
    {
      ...data,
      password: data?.password ? aesEncrypt(data?.password) : null,
    },
    (value) => value != null && value !== ""
  );

  const response = await fetcher<LoginResponse>("/v1/email/signin", {
    method: "POST",
    body: JSON.stringify(_data),
    needAuth: false,
  });

  return response;
};
